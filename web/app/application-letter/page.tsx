'use client';

import { useState, useEffect, useRef } from 'react';
import { authFetch } from '@/lib/authFetch';
import { InputMethod } from '../../types/InputMethod';
import ZoomableResult from '@/components/ZoomableResult';
import { useForm } from '@/hooks/useForm';
import { useResume } from '@/hooks/useResume';
import { useAnalytics } from '@/hooks/useAnalytics';
import { useFeedbackTracker } from '@/hooks/useFeedbackTracker';
import { useStepNavigation, FourSteps } from '@/hooks/useStepNavigation';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import Seo from '@/components/Seo';
import Toast from '@/components/Toast';
import JobInfoInput from '@/components/JobInfoInput';
import FeedbackForm from '@/components/FeedbackForm';
import { authPost } from '@/lib/authFetch';
import { useAuth } from '@/hooks/useAuth';
import Image from "next/legacy/image";
import { applicationLetterTemplates } from '@/utils/letter-templates/applicationLetterTemplates';
import { createToast } from '@/utils/notificationUtils';
import TemplatePricing from '@/components/TemplatePricing';
import { ApplicationLetterOutput } from '@/types/ApplicationLetterOutput';
import { useLetterHistory } from '@/hooks/useLetterHistory';
import { StructuredLetterData, convertStructuredDataToPlainText, convertToLetterTemplateData } from '@/types/letter-structured';
import { motion, AnimatePresence } from 'framer-motion';
import { startGeneration, useGenerationStatus, LetterGenerationParams } from '@/hooks/useLetterGeneration';

// Icon Components
const CheckIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
  </svg>
);

const ArrowRightIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
  </svg>
);

const ArrowLeftIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h17" />
  </svg>
);

// --- Bottom Sheet Component ---
interface BottomSheetProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}

function BottomSheet({ isOpen, onClose, title, children }: BottomSheetProps) {
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const handleDragEnd = (event: any, info: any) => {
    // Close if dragged down more than 150px or with sufficient velocity
    if (info.offset.y > 150 || info.velocity.y > 500) {
      onClose();
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
            onClick={onClose}
          />

          {/* Bottom Sheet */}
          <motion.div
            initial={{ y: '100%' }}
            animate={{ y: '0%' }}
            exit={{ y: '100%' }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
            className="fixed inset-x-0 bottom-0 z-50 md:hidden"
            drag="y"
            dragConstraints={{ top: 0, bottom: 0 }}
            dragElastic={{ top: 0, bottom: 0.5 }}
            onDragEnd={handleDragEnd}
            whileDrag={{ cursor: 'grabbing' }}
          >
            <div className="bg-white rounded-t-xl shadow-lg max-h-[80vh] flex flex-col">
              {/* Header with drag indicator */}
              <div className="flex flex-col items-center">
                {/* Drag Handle */}
                <div className="w-12 h-1 bg-gray-300 rounded-full mt-3" />

                <div className="flex items-center justify-between w-full p-4 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
                  <button
                    onClick={onClose}
                    className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
                  >
                    <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>

              {/* Content */}
              <div className="flex-1 overflow-y-auto p-4">
                {children}
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}

const XIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
  </svg>
);

// Step Indicator Component
function StepIndicator({ step, currentStep, label }: { step: number; currentStep: number; label: string }) {
  const isActive = step === currentStep;
  const isCompleted = step < currentStep;

  return (
    <div className="flex flex-col items-center gap-1 sm:gap-2 relative text-center min-w-0 flex-1">
      <div
        className={`w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center rounded-full border-2 font-bold transition-all duration-300 flex-shrink-0 text-xs sm:text-base ${
          isCompleted ? "bg-primary border-primary text-white" : isActive ? "border-primary text-primary scale-110" : "border-gray-300 text-gray-400"
        }`}
      >
        {isCompleted ? <CheckIcon className="w-4 h-4 sm:w-6 sm:h-6" /> : step}
      </div>
      <span className={`font-medium text-[10px] sm:text-xs md:text-sm leading-tight text-center max-w-[60px] sm:max-w-none ${isActive || isCompleted ? "text-gray-900" : "text-gray-500"}`}>{label}</span>
    </div>
  );
}

// Letter History Modal Component
function LetterHistoryModal({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) {
  const auth = useAuth();
  const { letters: letterHistory, isLoading: isLoadingHistory, error: historyError, refetch: refetchHistory } = useLetterHistory(auth);
  const [expandedLetters, setExpandedLetters] = useState<Set<string>>(new Set());
  const [currentHistoryPage, setCurrentHistoryPage] = useState(1);
  const [downloadingLetterId, setDownloadingLetterId] = useState<string | null>(null);
  
  const lettersPerPage = 5;
  const totalHistoryPages = Math.ceil(letterHistory.length / lettersPerPage);
  const startIndex = (currentHistoryPage - 1) * lettersPerPage;
  const endIndex = startIndex + lettersPerPage;
  const currentLetters = letterHistory.slice(startIndex, endIndex);

  const handlePageChange = (newPage: number) => {
    setCurrentHistoryPage(newPage);
  };

  const toggleLetterExpansion = (letterId: string) => {
    setExpandedLetters(prev => {
      const newSet = new Set(prev);
      if (newSet.has(letterId)) {
        newSet.delete(letterId);
      } else {
        newSet.add(letterId);
      }
      return newSet;
    });
  };

  // Handle downloading previous letters
  const handleDownloadPreviousLetter = async (letterId: string, templateName: string) => {
    setDownloadingLetterId(letterId);

    try {
      const response = await authFetch(`/api/edge/letters/${letterId}/download`, {
        method: 'GET',
      });

      if (!response.ok) {
        throw new Error('Failed to download letter');
      }

      // Get PDF as blob
      const pdfBlob = await response.blob();

      // Download the file
      const downloadLink = document.createElement('a');
      const url = window.URL.createObjectURL(pdfBlob);
      downloadLink.href = url;
      const fileName = `Surat_Lamaran_Gigsta_${templateName.replace(/\s+/g, '_')}`;
      downloadLink.download = `${fileName}.pdf`;
      document.body.appendChild(downloadLink);
      downloadLink.click();

      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(downloadLink);

    } catch (err) {
      console.error('Error downloading previous letter:', err);
    } finally {
      setDownloadingLetterId(null);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden flex flex-col">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex flex-row items-center justify-between gap-4">
            <h3 className="text-lg font-semibold text-gray-900">Riwayat Surat Lamaran</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 p-2"
            >
              <XIcon className="w-6 h-6" />
            </button>
          </div>
        </div>

        <div className="flex-1 overflow-y-auto p-6">
          {isLoadingHistory ? (
            <div className="flex justify-center py-8">
              <svg className="animate-spin h-8 w-8 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </div>
          ) : historyError ? (
            <div className="text-center py-8 text-red-600">
              <p>Gagal memuat riwayat surat: {historyError}</p>
              <button
                onClick={refetchHistory}
                className="mt-2 text-primary hover:text-primary-dark text-sm underline"
              >
                Coba lagi
              </button>
            </div>
          ) : letterHistory.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <svg className="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              <p>Belum ada surat lamaran yang dibuat</p>
              <p className="text-sm mt-1">Surat yang Anda buat akan muncul di sini</p>
            </div>
          ) : (
            <>
              <div className="space-y-4">
                {currentLetters.map((letter) => {
                  const isExpanded = expandedLetters.has(letter.id);
                  const previewLength = 120;
                  const shouldShowMore = letter.plainText.length > previewLength;
                  const displayText = isExpanded ? letter.plainText : letter.plainText.substring(0, previewLength);

                  return (
                    <div key={letter.id} className="bg-gradient-to-r from-slate-50 to-gray-50 border-l-4 border-l-blue-400 rounded-r-lg p-3 sm:p-4 hover:shadow-sm transition-all duration-200 hover:from-slate-100 hover:to-gray-100">
                      {/* Header with date */}
                      <div className="flex items-center gap-3 mb-4">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                          <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                          </svg>
                        </div>
                        <p className="text-xs text-gray-500 flex-shrink-0">
                          {new Date(letter.createdAt).toLocaleDateString('id-ID', {
                            day: '2-digit',
                            month: 'short',
                            year: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </p>
                        {/* Download button - only visible on desktop */}
                        {letter.designHtml && (
                          <button
                            onClick={() => handleDownloadPreviousLetter(letter.id, letter.templateName)}
                            disabled={downloadingLetterId === letter.id}
                            className="hidden sm:inline-flex items-center justify-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed ml-auto"
                          >
                            {downloadingLetterId === letter.id ? (
                              <>
                                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Mengunduh...
                              </>
                            ) : (
                              <>
                                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                Unduh PDF
                              </>
                            )}
                          </button>
                        )}
                      </div>

                      {/* Template name */}
                      <div className="mb-4">
                        <div className="mb-2">
                          <label className="text-xs font-medium text-gray-600 uppercase tracking-wide">Template:</label>
                        </div>
                        <div className="bg-white/70 backdrop-blur-sm p-2.5 sm:p-3 rounded border border-blue-200/50 text-sm sm:text-base font-medium text-gray-800 break-words">
                          {letter.templateName}
                        </div>
                      </div>

                      {/* Letter content */}
                      <div>
                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 mb-2">
                          <label className="text-xs font-medium text-gray-600 uppercase tracking-wide">Teks Surat:</label>
                          <button
                            className="px-2 py-1 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors flex items-center justify-center gap-1 text-xs font-medium self-start sm:self-auto"
                            onClick={() => {
                              navigator.clipboard.writeText(letter.plainText);
                              alert('Teks surat disalin ke clipboard!');
                            }}
                            title="Salin Teks Surat"
                          >
                            <svg className="h-4 w-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"></path>
                            </svg>
                            <span>Salin</span>
                          </button>
                        </div>
                        <div className="bg-white/70 backdrop-blur-sm p-3 sm:p-4 rounded border border-blue-200/50 min-h-[100px]">
                          <pre className="whitespace-pre-wrap text-xs sm:text-sm text-gray-700 leading-relaxed break-words overflow-hidden">
                            {displayText}
                            {!isExpanded && shouldShowMore && (
                              <span className="text-gray-400">...</span>
                            )}
                          </pre>
                          {shouldShowMore && (
                            <button
                              onClick={() => toggleLetterExpansion(letter.id)}
                              className="mt-3 text-xs sm:text-sm text-blue-600 hover:text-blue-800 font-medium focus:outline-none flex items-center gap-1 w-full sm:w-auto justify-center sm:justify-start"
                            >
                              {isExpanded ? (
                                <>
                                  <svg className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 15l7-7 7 7"></path>
                                  </svg>
                                  <span>Tampilkan lebih sedikit</span>
                                </>
                              ) : (
                                <>
                                  <svg className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                                  </svg>
                                  <span>Tampilkan selengkapnya</span>
                                </>
                              )}
                            </button>
                          )}
                        </div>
                      </div>

                      {/* Download button - only visible on mobile, at the bottom */}
                      {letter.designHtml && (
                        <div className="mt-4 sm:hidden">
                          <button
                            onClick={() => handleDownloadPreviousLetter(letter.id, letter.templateName)}
                            disabled={downloadingLetterId === letter.id}
                            className="w-full inline-flex items-center justify-center px-4 py-3 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            {downloadingLetterId === letter.id ? (
                              <>
                                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Mengunduh...
                              </>
                            ) : (
                              <>
                                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                Unduh PDF
                              </>
                            )}
                          </button>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>

              {/* Pagination Controls */}
              {totalHistoryPages > 1 && (
                <div className="mt-6 pt-4 border-t border-gray-200">
                  <div className="text-xs sm:text-sm text-gray-600 text-center mb-4 px-2">
                    Menampilkan {startIndex + 1}-{Math.min(endIndex, letterHistory.length)} dari {letterHistory.length} surat
                  </div>
                  <div className="flex items-center justify-center space-x-1 px-2">
                    <button
                      onClick={() => handlePageChange(Math.max(currentHistoryPage - 1, 1))}
                      disabled={currentHistoryPage === 1}
                      className="px-2 sm:px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed min-w-[36px] flex items-center justify-center"
                    >
                      ‹
                    </button>

                    {Array.from({ length: totalHistoryPages }, (_, i) => i + 1).map(page => (
                      <button
                        key={page}
                        onClick={() => handlePageChange(page)}
                        className={`px-2 sm:px-3 py-2 text-sm font-medium rounded-md min-w-[36px] flex items-center justify-center ${
                          currentHistoryPage === page
                            ? 'bg-primary text-white'
                            : 'text-gray-500 bg-white border border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        {page}
                      </button>
                    ))}

                    <button
                      onClick={() => handlePageChange(Math.min(currentHistoryPage + 1, totalHistoryPages))}
                      disabled={currentHistoryPage === totalHistoryPages}
                      className="px-2 sm:px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed min-w-[36px] flex items-center justify-center"
                    >
                      ›
                    </button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}

export default function ApplicationLetterPage() {
  const auth = useAuth();
  const { trackEvent, logError } = useAnalytics();
  const { user, signIn, signUp, signInWithGoogle } = auth;
  
  // Ref for step indicator area
  const stepIndicatorRef = useRef<HTMLDivElement>(null);

  // Wizard state using useStepNavigation hook
  const stepNavigation = useStepNavigation<FourSteps>({
    initialStep: 1,
    totalSteps: 4,
    stepParam: 'step',
    syncWithURL: true,
    onStepValidation: async (currentStep, targetStep) => {
      // Step 1 validation: require resume upload
      if (currentStep === 1 && targetStep > 1) {
        if (!uploadSuccess || !existingResume) {
          setLetterGenerationError('Harap unggah resume terlebih dahulu');
          return false;
        }
      }
      
      // Step 2 validation: require job information
      if (currentStep === 2 && targetStep > 2) {
        const { jobDescription, jobImage } = form.values;
        if (inputMethod === InputMethod.TEXT && !jobDescription) {
          setLetterGenerationError('Harap isi informasi lowongan');
          return false;
        }
        if (inputMethod === InputMethod.IMAGE && !jobImage) {
          setLetterGenerationError('Harap unggah poster lowongan');
          return false;
        }
      }
      
      return true;
    },
    onStepChange: (newStep, prevStep) => {
      // Clear errors when step changes
      setLetterGenerationError('');
      setLetterResultError('');
      
      // Scroll to step indicator if user is scrolled below it
      if (stepIndicatorRef.current) {
        const stepIndicatorRect = stepIndicatorRef.current.getBoundingClientRect();
        const isStepIndicatorAboveViewport = stepIndicatorRect.top < 0;
        
        if (isStepIndicatorAboveViewport) {
          stepIndicatorRef.current.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      }
    }
  });
  
  const { currentStep, goNext, goBack } = stepNavigation;
  const [showHistoryModal, setShowHistoryModal] = useState(false);
  
  // Track page view
  useEffect(() => {
    trackEvent('Application Letter Page Viewed');
  }, []);
  
  // Use the new useResume hook
  const {
    isUploading,
    uploadSuccess,
    error,
    toast,
    existingResume,
    isLoading,
    isDeleting,
    isGettingResumeUrl,
    setToast,
    handleResumeUpload,
    handleViewResume,
    handleDeleteResume
  } = useResume(auth);
  
  // Form state
  const form = useForm<{
    jobDescription: string;
    jobImage: File | null;
  }>({
    jobDescription: '',
    jobImage: null,
  });
  
  const [generatedLetter, setGeneratedLetter] = useState<ApplicationLetterOutput | null>(null);
  const [isLetterEditable, setIsLetterEditable] = useState(false);
  const [editedLetter, setEditedLetter] = useState('');
  const [isStructuredEditable, setIsStructuredEditable] = useState(false);
  const [editedStructuredData, setEditedStructuredData] = useState<StructuredLetterData | null>(null);
  const [isRegenerating, setIsRegenerating] = useState(false);
  const [isDownloadingPdf, setIsDownloadingPdf] = useState(false);
  const [inputMethod, setInputMethod] = useState<InputMethod>(InputMethod.TEXT);
  
  // New async generation state
  const [generationId, setGenerationId] = useState<string | null>(null);
  const [isStartingGeneration, setIsStartingGeneration] = useState(false);
  const generationStatus = useGenerationStatus(generationId);
  
  // Computed properties based on generation status
  const isGenerating = generationStatus.status === "processing" || isStartingGeneration;
  const isStreaming = generationStatus.status === "processing" || isStartingGeneration;
  const streamingProgress = isGenerating ? "Memproses surat lamaran..." : "";
  
  // Template selection state
  const [selectedTemplateId, setSelectedTemplateId] = useState('plain-text');
  const [currentTemplatePage, setCurrentTemplatePage] = useState(0);
  const templatesPerPage = 4;
  const [isRecommendedExpanded, setIsRecommendedExpanded] = useState<{[key: string]: boolean}>({});

  const [letterGenerationError, setLetterGenerationError] = useState('');
  const [letterResultError, setLetterResultError] = useState('');

  // Bottom sheet states
  const [isPlainTextSheetOpen, setIsPlainTextSheetOpen] = useState(false);
  const [isEditSheetOpen, setIsEditSheetOpen] = useState(false);
  
  // Use feedback tracker
  const { shouldShowFeedback, markFeedbackShown, incrementFeatureUsage } = useFeedbackTracker('application-letter');
  const [showFeedback, setShowFeedback] = useState(false);

  // Login/Register modal state
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [loginMode, setLoginMode] = useState<'login' | 'register'>('register');
  const [loginForm, setLoginForm] = useState({ email: '', password: '', confirmPassword: '' });
  const [loginError, setLoginError] = useState('');
  const [loginSuccess, setLoginSuccess] = useState('');
  const [isLoggingIn, setIsLoggingIn] = useState(false);


  // Handle login form submission
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoggingIn(true);
    setLoginError('');
    setLoginSuccess('');

    try {
      if (loginMode === 'register' && loginForm.password !== loginForm.confirmPassword) {
        setLoginError('Password tidak cocok');
        setIsLoggingIn(false);
        return;
      }

      const result = loginMode === 'login'
        ? await signIn(loginForm.email, loginForm.password)
        : await signUp(loginForm.email, loginForm.password);

      if (result.success) {
        if (loginMode === 'register') {
          setLoginMode('login');
          setLoginForm({ email: loginForm.email, password: '', confirmPassword: '' });
          setLoginSuccess('Pendaftaran berhasil! Silakan periksa email Anda untuk konfirmasi, lalu kembali ke tab ini untuk masuk');
        } else {
          setShowLoginModal(false);
          setLoginForm({ email: '', password: '', confirmPassword: '' });
          setToast(createToast('Berhasil masuk', 'success'));
        }
      } else {
        setLoginError(result.message || 'Terjadi kesalahan saat masuk');
      }
    } catch (err) {
      setLoginError('Terjadi kesalahan saat masuk');
    } finally {
      setIsLoggingIn(false);
    }
  };

  // Handle Google login
  const handleGoogleLogin = async () => {
    setIsLoggingIn(true);
    try {
      const result = await signInWithGoogle(encodeURIComponent(window.location.pathname));
      if (result.success && result.message) {
        window.open(result.message, '_blank');
        setLoginSuccess('Silakan selesaikan login di tab baru, lalu kembali ke halaman ini');
      }
      if (result.error) {
        if (result.message) {
          setLoginError(result.message);
        } else {
          throw result.error;
        }
      }
    } catch (error: any) {
      setLoginError('Gagal login dengan Google');
    } finally {
      setIsLoggingIn(false);
    }
  };

  // Handle user login completion (for OAuth flow)
  useEffect(() => {
    if (user && showLoginModal) {
      setShowLoginModal(false);
      setLoginForm({ email: '', password: '', confirmPassword: '' });
      setLoginError('');
      setLoginSuccess('');
      setToast(createToast('Berhasil masuk', 'success'));
    }
  }, [user, showLoginModal]);

  // Step navigation functions are now provided by useStepNavigation hook

  // Handle job poster image upload
  const handleJobImageChange = (file: File | null) => {
    form.setValues(prev => ({ ...prev, jobImage: file }));
    setLetterGenerationError('');
    setLetterResultError('');
  };

  // Download designed letter as PDF
  const downloadGeneratedLetter = async () => {
    if (!generatedLetter?.design) {
      console.error('Design not available');
      setLetterResultError('Tidak ada desain untuk diunduh.');
      return;
    }
    
    if (!generatedLetter.design.html || generatedLetter.design.html.trim() === '') {
      console.error('Selected design has no HTML content');
      setLetterResultError('Desain yang dipilih tidak memiliki konten untuk diunduh.');
      return;
    }

    if (!generatedLetter.letterId) {
      console.error('Letter ID not available');
      setLetterResultError('ID surat tidak ditemukan. Silakan buat ulang surat Anda dan coba lagi.');
      return;
    }
    
    trackEvent('Application Letter Download Attempt', {
      format: 'pdf',
      type: 'designed',
      design_name: generatedLetter.design.name
    });
    
    setIsDownloadingPdf(true);
    
    try {
      const response = await authFetch(`/api/edge/letters/${generatedLetter.letterId}/download`, {
        method: 'GET',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`API error: ${errorData.error || response.statusText}`);
      }
      
      const pdfBlob = await response.blob();
      
      const downloadLink = document.createElement('a');
      const url = window.URL.createObjectURL(pdfBlob);
      downloadLink.href = url;
      const fileName = `Surat_Lamaran_Gigsta_${generatedLetter.design.name.replace(/\s+/g, '_')}`;
      downloadLink.download = `${fileName}.pdf`;
      document.body.appendChild(downloadLink);
      downloadLink.click();
      
      window.URL.revokeObjectURL(url);
      document.body.removeChild(downloadLink);
      
      trackEvent('Application Letter Downloaded', {
        format: 'pdf',
        type: 'designed',
        design_name: generatedLetter.design.name
      });
    } catch (err) {
      console.error('Error creating designed PDF:', err);
      setLetterResultError('Gagal membuat file PDF. Silakan coba lagi.');
      
      trackEvent('Application Letter Download Error', {
        error: err instanceof Error ? err.message : 'unknown_error',
        format: 'pdf',
        type: 'designed'
      });
      
      logError(err instanceof Error ? err : new Error('Failed to create designed PDF'), {
        feature: 'application_letter',
        action: 'download_designed_pdf'
      }, 'critical');
    } finally {
      setIsDownloadingPdf(false);
    }
  };

  const handleDownloadPdf = async () => {
    if (!generatedLetter?.design) {
      setLetterResultError('Tidak ada desain untuk diunduh.');
      return;
    }

    const selectedTemplate = applicationLetterTemplates.find(t => t.id === selectedTemplateId);
    if (!generatedLetter.letterId) {
      setLetterResultError('ID surat tidak ditemukan. Silakan buat ulang surat Anda dan coba lagi.');
      return;
    }

    if (!selectedTemplate) {
      setLetterResultError('Template tidak ditemukan. Silakan coba lagi.');
      return;
    }

    await downloadGeneratedLetter();
  };

  const generateLetter = async () => {
    if (!user) {
      setLoginMode('register');
      setShowLoginModal(true);
      return;
    }

    // Validate template selection
    if (!selectedTemplateId) {
      setLetterGenerationError('Harap pilih template terlebih dahulu');
      return;
    }

    incrementFeatureUsage();
    setShowFeedback(shouldShowFeedback);
    setGeneratedLetter(null);
    setIsLetterEditable(false);

    if (!existingResume) {
      setLetterGenerationError('Harap unggah resume terlebih dahulu');
      trackEvent('Application Letter Error', { error: 'no_resume' });
      return;
    }

    const { jobDescription, jobImage } = form.values;
    if (inputMethod === InputMethod.TEXT && !jobDescription) {
      setLetterGenerationError('Harap isi informasi lowongan');
      trackEvent('Job Match Error', { error: 'incomplete_form_text' });
      return;
    }
    if (inputMethod === InputMethod.IMAGE && !jobImage) {
      setLetterGenerationError('Harap unggah poster lowongan');
      trackEvent('Job Match Error', { error: 'incomplete_form_image' });
      return;
    }

    trackEvent('Application Letter Generation Attempt', {
      description_length: jobDescription.length,
      used_image: !!jobImage,
      template_id: selectedTemplateId
    });

    // Move to step 4 when generation starts
    if (currentStep === 3) {
      stepNavigation.goToStep(4 as FourSteps);
    }

    setLetterGenerationError('');
    setLetterResultError('');
    setIsStartingGeneration(true);

    try {
      // Prepare parameters for the new async generation
      const params: LetterGenerationParams = {
        jobDescription: inputMethod === InputMethod.TEXT ? jobDescription : undefined,
        jobImage: inputMethod === InputMethod.IMAGE && jobImage ? jobImage : undefined,
        templateId: selectedTemplateId,
        ...(existingResume?.unauthenticatedResumeFile && existingResume?.fileName && {
          unauthenticatedResumeFile: existingResume.unauthenticatedResumeFile,
          unauthenticatedResumeFileName: existingResume.fileName
        }),
      };

      // Start the async generation
      const newGenerationId = await startGeneration(params);
      setGenerationId(newGenerationId);
    } catch (err) {
      setIsStartingGeneration(false);
      setLetterGenerationError('Gagal membuat surat lamaran. Silakan coba lagi.');
      console.error(err);
      
      logError(err instanceof Error ? err : new Error('Failed to generate application letter'), {
        feature: 'application_letter',
        action: 'generate',
        description_length: form.values.jobDescription.length
      }, 'error');
    }
  };

  // Handle generation status updates
  useEffect(() => {
    if (!generationId) return;

    // Reset starting generation flag once we get any real status update
    if (generationStatus.status !== "idle" && isStartingGeneration) {
      setIsStartingGeneration(false);
    }

    if (generationStatus.status === "done") {
      // Convert the generation result to the existing format
      const selectedTemplate = applicationLetterTemplates.find(t => t.id === selectedTemplateId);
      
      setGeneratedLetter({
        plainText: generationStatus.plainText,
        structuredData: generationStatus.structuredData,
        letterId: generationId,
        design: generationStatus.designHtml ? {
          templateId: selectedTemplateId,
          html: generationStatus.designHtml,
          name: selectedTemplate?.name || '',
          previewDescription: selectedTemplate?.previewDescription || '',
          fontFamily: selectedTemplate?.fontFamily || 'Arial, sans-serif',
          recommended: selectedTemplate?.recommended || false
        } : undefined
      });

      trackEvent('Application Letter Generated Successfully', {
        design_template_id: selectedTemplateId,
        used_job_image: !!form.values.jobImage,
        streaming: false,
        generation_mode: 'async'
      });

      // Clear generation ID after successful completion
      setGenerationId(null);
    } else if (generationStatus.status === "error") {
      setIsStartingGeneration(false);
      setLetterGenerationError(generationStatus.error || 'Terjadi kesalahan saat membuat surat lamaran');
      
      trackEvent('Application Letter Generation Failed', {
        error: generationStatus.error || 'unknown_error'
      });

      // Clear generation ID after error
      setGenerationId(null);
    }
  }, [generationStatus, generationId, selectedTemplateId, form.values.jobImage, isStartingGeneration]);

  // Regenerate letter from edited structured data
  const regenerateLetterFromStructuredEdit = async () => {
    if (!user) {
      setLoginMode('register');
      setShowLoginModal(true);
      return;
    }

    if (!editedStructuredData) {
      setLetterGenerationError('Data tidak ditemukan');
      return;
    }

    setIsRegenerating(true);
    setLetterGenerationError('');
    setLetterResultError('');

    try {
      trackEvent('Application Letter Regenerate From Structured Edit', {
        template_id: selectedTemplateId,
      });

      const body: any = {
        jobDescription: form.values.jobDescription,
        templateId: selectedTemplateId,
        existingLetterId: generatedLetter?.letterId,
        editedStructuredData: JSON.stringify(editedStructuredData)
      };

      if (form.values.jobImage) {
        body.jobImage = form.values.jobImage;
      }

      if (existingResume?.unauthenticatedResumeFile && existingResume?.fileName) {
        body.unauthenticatedResumeFile = existingResume.unauthenticatedResumeFile;
        body.unauthenticatedResumeFileName = existingResume.fileName;
      }

      const response = await authPost('/api/generate-application-letter', body);
      const data = await response.json();

      if (data.data.structuredData) {
        const selectedTemplate = applicationLetterTemplates.find(t => t.id === selectedTemplateId);
        const plainText = convertStructuredDataToPlainText(data.data.structuredData);

        setGeneratedLetter({
          plainText,
          structuredData: data.data.structuredData,
          letterId: data.data.letterId,
          design: data.data.design ? {
            templateId: selectedTemplateId,
            html: data.data.design,
            name: selectedTemplate?.name || '',
            previewDescription: selectedTemplate?.previewDescription || '',
            fontFamily: selectedTemplate?.fontFamily || 'Arial, sans-serif',
            recommended: selectedTemplate?.recommended || false
          } : undefined
        });

        setIsStructuredEditable(false);
        setEditedStructuredData(null); // Clear the edited data
        setLetterGenerationError('');
        setLetterResultError('');

        trackEvent('Application Letter Regenerated Successfully', {
          design_template_id: selectedTemplateId,
          used_job_image: !!form.values.jobImage,
          generation_mode: 'structured_edit'
        });
      } else {
        setLetterGenerationError(data.error || 'Terjadi kesalahan saat memperbarui surat lamaran');
        trackEvent('Application Letter Regenerate Failed', {
          error: data.error || 'unknown_error'
        });
      }
    } catch (err) {
      setLetterGenerationError('Gagal memperbarui surat lamaran. Silakan coba lagi.');
      logError(err instanceof Error ? err : new Error('Failed to regenerate application letter from structured edit'), {
        feature: 'application_letter',
        action: 'regenerate_from_structured_edit'
      }, 'error');
    } finally {
      setIsRegenerating(false);
    }
  };

  return (
   <main className="min-h-screen flex flex-col pt-16">
      <Toast 
        show={toast.show} 
        message={toast.message} 
        type={toast.type} 
        onClose={() => setToast({ ...toast, show: false })} 
      />
      <Seo 
        title="Buat Surat Lamaran Kerja Otomatis dengan AI"
        description="Dapatkan surat lamaran kerja yang dipersonalisasi secara otomatis oleh AI Gigsta. Tingkatkan peluang Anda dilirik HR dan diundang interview dengan dokumen yang tepat."
        canonical="https://gigsta.io/application-letter"
      />
      <Navbar auth={auth} />

      {/* Sticky Banner for Unauthenticated Users */}
      {!auth.loading && !user && (
        <div className="sticky top-[4.5rem] z-10 bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 text-white py-2 sm:py-3 px-4 shadow-lg">
          <div className="max-w-7xl mx-auto flex items-center justify-between gap-3">
            <div className="flex items-center space-x-2 sm:space-x-3 min-w-0 flex-1">
              <div className="flex items-center space-x-1 sm:space-x-2 flex-shrink-0">
                <div className="relative">
                  <svg className="w-4 h-4 sm:w-5 sm:h-5 text-yellow-300 animate-spin" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z"/>
                  </svg>
                  <div className="absolute inset-0 w-4 h-4 sm:w-5 sm:h-5 bg-yellow-300 rounded-full opacity-20 animate-ping"></div>
                </div>
                <span className="font-bold text-sm sm:text-base">🎉</span>
              </div>
              <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-2 min-w-0">
                <span className="font-bold text-xs sm:text-sm md:text-base leading-tight">
                  BONUS 10 TOKEN GRATIS!
                </span>
                <span className="text-xs sm:text-sm opacity-90 leading-tight">
                  untuk pengguna baru yang mendaftar
                </span>
              </div>
            </div>
            <button
              onClick={() => {
                setLoginMode('register');
                setShowLoginModal(true)
              }}
              className="bg-white text-purple-600 hover:bg-yellow-100 font-bold py-1.5 px-3 sm:py-2 sm:px-4 rounded-full text-xs sm:text-sm text-center transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl flex-shrink-0"
            >
              Daftar Sekarang
            </button>
          </div>
        </div>
      )}

      {/* Feedback Modal */}
      {showFeedback && (
        <div className="fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-50 flex items-center justify-center p-4">
          <FeedbackForm
            featureType="application-letter"
            onClose={() => {
              setShowFeedback(false);
              markFeedbackShown();
            }}
          />
        </div>
      )}

      {/* Login/Register Modal */}
      {showLoginModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex flex-row items-center justify-between gap-4">
                <h3 className="text-lg font-medium text-gray-900">
                  {loginMode === 'login' ? 'Masuk ke Akun' : 'Daftar Akun Baru'}
                </h3>
                <button
                  onClick={() => {
                    setShowLoginModal(false);
                    setLoginError('');
                    setLoginSuccess('');
                    setLoginForm({ email: '', password: '', confirmPassword: '' });
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XIcon className="w-6 h-6" />
                </button>
              </div>
            </div>
            
            <div className="px-6 py-4">
              <p className="text-sm text-gray-600 mb-4">
                {loginMode === 'login'
                  ? 'Masuk untuk membuat surat lamaran Anda'
                  : 'Daftar untuk membuat surat lamaran Anda'
                }
              </p>

              {loginSuccess && (
                <div className="mb-4 p-3 bg-green-100 border border-green-200 text-green-700 rounded-md text-sm">
                  {loginSuccess}
                </div>
              )}
              
              {loginError && (
                <div className="mb-4 p-3 bg-red-100 border border-red-200 text-red-700 rounded-md text-sm">
                  {loginError}
                </div>
              )}

              <form onSubmit={handleLogin} className="space-y-4">
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                    Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    value={loginForm.email}
                    onChange={(e) => setLoginForm(prev => ({ ...prev, email: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                    disabled={isLoggingIn}
                  />
                </div>
                
                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                    Password
                  </label>
                  <input
                    type="password"
                    id="password"
                    value={loginForm.password}
                    onChange={(e) => setLoginForm(prev => ({ ...prev, password: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                    disabled={isLoggingIn}
                  />
                </div>
                
                {loginMode === 'register' && (
                  <div>
                    <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                      Konfirmasi Password
                    </label>
                    <input
                      type="password"
                      id="confirmPassword"
                      value={loginForm.confirmPassword}
                      onChange={(e) => setLoginForm(prev => ({ ...prev, confirmPassword: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                      disabled={isLoggingIn}
                    />
                  </div>
                )}

                <button
                  type="submit"
                  disabled={isLoggingIn}
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoggingIn ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      {loginMode === 'login' ? 'Masuk...' : 'Mendaftar...'}
                    </>
                  ) : (
                    loginMode === 'login' ? 'Masuk' : 'Daftar'
                  )}
                </button>
              </form>

              <div className="mt-4">
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-300" />
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-2 bg-white text-gray-500">atau</span>
                  </div>
                </div>

                <button
                  onClick={handleGoogleLogin}
                  disabled={isLoggingIn}
                  className="mt-3 w-full flex justify-center items-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                  </svg>
                  Masuk dengan Google
                </button>
              </div>

              <div className="mt-4 text-center">
                <button
                  onClick={() => {
                    setLoginMode(loginMode === 'login' ? 'register' : 'login');
                    setLoginError('');
                    setLoginSuccess('');
                  }}
                  className="text-sm text-blue-600 hover:text-blue-500"
                  disabled={isLoggingIn}
                >
                  {loginMode === 'login'
                    ? 'Belum punya akun? Daftar di sini'
                    : 'Sudah punya akun? Masuk di sini'
                  }
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Letter History Modal */}
      <LetterHistoryModal isOpen={showHistoryModal} onClose={() => setShowHistoryModal(false)} />

      <section className="py-12 flex-grow">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Buat Surat Lamaran</h1>
            <p className="mt-4 text-lg text-gray-600">
              Dapatkan dokumen lamaran kerja profesional yang dipersonalisasi dari CV/resume dan detail lowongan Anda—tingkatkan peluang lolos seleksi!
            </p>
          </div>

          {/* Step Indicator */}
          <div ref={stepIndicatorRef} className="flex items-start justify-center mb-6 sm:mb-8 relative px-4">
            <div className="flex items-start justify-between w-full max-w-3xl">
              <StepIndicator step={1} currentStep={currentStep} label="Upload Resume" />
              <div className={`flex-1 h-0.5 mx-1 sm:mx-2 mt-4 sm:mt-5 ${currentStep > 1 ? 'bg-primary' : 'bg-gray-300'}`}></div>
              <StepIndicator step={2} currentStep={currentStep} label="Info Pekerjaan" />
              <div className={`flex-1 h-0.5 mx-1 sm:mx-2 mt-4 sm:mt-5 ${currentStep > 2 ? 'bg-primary' : 'bg-gray-300'}`}></div>
              <StepIndicator step={3} currentStep={currentStep} label="Pilih Template" />
              <div className={`flex-1 h-0.5 mx-1 sm:mx-2 mt-4 sm:mt-5 ${currentStep > 3 ? 'bg-primary' : 'bg-gray-300'}`}></div>
              <StepIndicator step={4} currentStep={currentStep} label="Hasil" />
            </div>
          </div>
          
          <div className="bg-white shadow-md rounded-lg p-6 mb-8">
            {/* Step 1: Resume Upload */}
            {currentStep === 1 && (
              <div>
                <h2 className="text-xl font-semibold mb-4">1. Upload CV/Resume Anda</h2>
                
                {isLoading ? (
                  <div className="flex justify-center py-8">
                    <svg className="animate-spin h-8 w-8 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  </div>
                ) : uploadSuccess && existingResume ? (
                  <div className="border-2 border-green-200 rounded-lg p-4 sm:p-6 bg-green-50">
                    <div className="flex items-center mb-3">
                      <svg className="w-6 h-6 text-green-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      <h3 className="font-medium text-green-700">Resume aktif tersedia!</h3>
                    </div>
                    
                    <div className="mb-4">
                      <p className="text-gray-700 mb-1 break-words">
                        File: <span className="font-mono text-sm">{existingResume.fileName.split('_').pop()}</span>
                      </p>
                      <p className="text-sm text-gray-500">
                        Diunggah pada: {new Date(existingResume.uploadedAt).toLocaleDateString('id-ID', {
                          day: 'numeric',
                          month: 'long',
                          year: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </p>
                    </div>
                    
                    <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
                      {isGettingResumeUrl ? (
                        <div className="inline-flex items-center text-sm text-primary w-fit">
                          <svg className="animate-spin h-4 w-4 mr-1 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Memuat...
                        </div>
                      ) : (
                        <button
                          onClick={handleViewResume}
                          className="inline-flex items-center text-sm text-primary hover:underline bg-transparent border-0 p-0 cursor-pointer w-fit"
                        >
                          <svg className="h-4 w-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                          </svg>
                          Lihat Resume
                        </button>
                      )}
                      
                      {isUploading ? (
                        <div className="inline-flex items-center text-sm text-gray-800 w-fit">
                          <svg className="animate-spin h-4 w-4 mr-1 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Mengunggah...
                        </div>
                      ) : (
                        <label
                          htmlFor="resume-existing"
                          className="inline-flex items-center text-sm text-gray-600 hover:text-gray-800 hover:underline cursor-pointer w-fit"
                        >
                          <svg className="h-4 w-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                          </svg>
                          Unggah Resume Baru
                        </label>
                      )}
                      
                      {isDeleting ? (
                        <div className="inline-flex items-center text-sm text-red-600 w-fit">
                          <svg className="animate-spin h-4 w-4 mr-1 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Menghapus...
                        </div>
                      ) : (
                        <button
                          onClick={handleDeleteResume}
                          className="inline-flex items-center text-sm text-red-600 hover:text-red-800 hover:underline w-fit"
                          type="button"
                        >
                          <svg className="h-4 w-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                          </svg>
                          Hapus Resume
                        </button>
                      )}
                      
                      <input
                        type="file"
                        id="resume-existing"
                        accept=".pdf,.docx,.png,.jpg,.jpeg"
                        className="hidden"
                        onChange={handleResumeUpload}
                        ref={(input) => {
                          if (input) {
                            input.value = '';
                          }
                        }}
                      />
                    </div>
                  </div>
                ) : (
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <input
                      type="file"
                      id="resume-new"
                      accept=".pdf,.docx,.png,.jpg,.jpeg"
                      className="hidden"
                      onChange={handleResumeUpload}
                      disabled={isUploading}
                      ref={(input) => {
                        if (input) {
                          input.value = '';
                        }
                      }}
                    />
                    <label
                      htmlFor="resume-new"
                      className={`cursor-pointer flex flex-col items-center justify-center`}
                    >
                      {isUploading ? (
                        <svg className="animate-spin h-12 w-12 text-primary mb-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                      ) : (
                        <svg className="w-12 h-12 text-gray-400 mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                        </svg>
                      )}
                      {isUploading ? (
                        <p className="text-gray-700 font-medium">Mengunggah Resume...</p>
                      ) : (
                        <>
                          <p className="text-gray-700 font-medium">Klik untuk mengunggah resume Anda</p>
                          <p className="text-sm text-gray-500 mt-1">PDF, DOCX, PNG, JPG, atau JPEG (Maks. 5MB)</p>
                        </>
                      )}
                    </label>
                  </div>
                )}
                
                {error && (
                  <div className="mt-4 p-3 bg-red-100 text-red-700 rounded-lg">
                    {error}
                  </div>
                )}

                {/* Info catatan jika user tidak login dan sudah upload resume */}
                {!user && existingResume && (
                  <p className="mt-2 text-xs text-gray-500">
                    *File CV/resume Anda disimpan secara lokal dan akan hilang jika halaman di-refresh. <b>Masuk</b> agar CV/resume tetap tersimpan sehingga tidak perlu mengunggah ulang
                  </p>
                )}

                {/* Navigation */}
                <div className="flex justify-end mt-6">
                  <button
                    onClick={goNext}
                    className="inline-flex items-center justify-center gap-2 bg-primary hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold shadow-sm transition-colors"
                  >
                    <span>Selanjutnya</span>
                    <ArrowRightIcon className="w-5 h-5" />
                  </button>
                </div>

                {/* Error message */}
                {letterGenerationError && (
                  <div className="p-3 mt-6 text-sm bg-red-100 border border-red-200 text-red-800 rounded-md">
                    <div className="flex">
                      <svg className="h-5 w-5 text-red-600 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      <span>{letterGenerationError}</span>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Step 2: Job Information Input */}
            {currentStep === 2 && (
              <div>
                <h2 className="text-xl font-semibold mb-4">2. Masukkan Informasi Lowongan</h2>
                <JobInfoInput
                  jobDescription={form.values.jobDescription}
                  onJobDescriptionChange={(value) => form.setValues(prev => ({ ...prev, jobDescription: value }))}
                  jobImage={form.values.jobImage}
                  onJobImageChange={handleJobImageChange}
                  onInputMethodChange={setInputMethod}
                  onError={setLetterGenerationError}
                />

                {/* Navigation */}
                <div className="flex justify-between mt-6">
                  {!isGenerating && !isRegenerating && !isStreaming && (
                    <button
                      onClick={goBack}
                      className="inline-flex items-center justify-center gap-2 border border-gray-300 hover:bg-gray-100 text-gray-800 px-6 py-3 rounded-lg font-semibold shadow-sm transition-colors"
                    >
                      <ArrowLeftIcon className="w-5 h-5" />
                      <span className="hidden sm:inline">Kembali</span>
                    </button>
                  )}
                  <button
                    onClick={goNext}
                    className="inline-flex items-center justify-center gap-2 bg-primary hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold shadow-sm transition-colors"
                  >
                    <span>Selanjutnya</span>
                    <ArrowRightIcon className="w-5 h-5" />
                  </button>
                </div>

                {/* Error message */}
                {letterGenerationError && (
                  <div className="p-3 mt-6 text-sm bg-red-100 border border-red-200 text-red-800 rounded-md">
                    <div className="flex">
                      <svg className="h-5 w-5 text-red-600 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      <span>{letterGenerationError}</span>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Step 3: Template Selection */}
            {currentStep === 3 && (
              <div>
                <h2 className="text-xl font-semibold mb-4">3. Pilih Template Surat</h2>

                <div className="mb-6">
                  {/* Mobile-optimized banner */}
                  <div className="mb-4 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg shadow-sm">
                    <div className="p-3 sm:p-4">
                      <div className="flex gap-2.5 sm:gap-3">
                        {/* Icon - top aligned on mobile, centered on desktop */}
                        <div className="flex-shrink-0 mt-0.5 sm:mt-0 sm:self-center">
                          <div className="bg-white/20 rounded-full p-1.5 sm:p-2">
                            <svg className="w-3.5 h-3.5 sm:w-4 sm:h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                            </svg>
                          </div>
                        </div>

                        {/* Text content - properly wrapping */}
                        <div className="flex-1 min-w-0">
                          <h3 className="text-white font-semibold text-sm sm:text-base leading-tight mb-1">
                            Tampil Beda dari Pelamar Lain ✨
                          </h3>
                          <p className="text-white/90 text-xs sm:text-sm leading-relaxed">
                            Pilih desain menarik yang membuat HRD terkesan dengan surat lamaran Anda
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                    
                    {/* Template selection grid */}
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                        {applicationLetterTemplates
                          .slice(currentTemplatePage * templatesPerPage, (currentTemplatePage + 1) * templatesPerPage)
                          .map(template => (
                          <div
                            key={template.id}
                            className={`border-2 rounded-lg overflow-hidden cursor-pointer transition-all duration-200 ${
                              selectedTemplateId === template.id
                                ? 'border-primary shadow-xl ring-4 ring-primary/20 bg-blue-50/50 transform scale-[1.02]'
                                : 'border-gray-200 hover:border-gray-300 hover:shadow-md'
                            }`}
                            onClick={() => setSelectedTemplateId(template.id)}
                          >
                            <div className="relative h-80 sm:h-96 md:h-[26rem] bg-gray-100">
                              <Image
                                src={template.previewImagePath}
                                alt={template.name}
                                layout="fill"
                                objectFit="contain"
                                className="p-3"
                              />

                              {/* Selected indicator */}
                              {selectedTemplateId === template.id && (
                                <div className="absolute top-3 left-3 bg-primary text-white rounded-full p-2 shadow-lg">
                                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                                  </svg>
                                </div>
                              )}
                              {template.recommended && (
                                <div
                                  className="absolute top-3 right-3 z-1 cursor-pointer"
                                  onClick={(e) => {
                                    e.stopPropagation(); // Prevent card click
                                    setIsRecommendedExpanded(prev => ({...prev, [template.id]: !prev[template.id]}));
                                  }}
                                >
                                  {isRecommendedExpanded[template.id] ? (
                                    // Expanded state: Pill label with text
                                    <div className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs font-semibold px-3 py-1.5 rounded-full shadow-lg flex items-center space-x-1">
                                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                      </svg>
                                      <span>DIREKOMENDASIKAN</span>
                                    </div>
                                  ) : (
                                    // Collapsed state: Star icon only
                                    <div className="w-6 h-6 bg-yellow-500 rounded-full flex justify-center items-center shadow-md">
                                      <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        className="h-3.5 w-3.5 text-white"
                                        viewBox="0 0 20 20"
                                        fill="currentColor"
                                      >
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                      </svg>
                                    </div>
                                  )}
                                </div>
                              )}
                              {template.isPremium ? (
                                <div className="absolute bottom-0 right-0 bg-blue-500 text-white text-sm font-bold px-3 py-1 m-3 rounded-md shadow-sm">
                                  PREMIUM
                                </div>
                              ) : (
                                <div className="absolute bottom-0 right-0 bg-green-500 text-white text-sm font-bold px-3 py-1 m-3 rounded-md shadow-sm">
                                  STANDAR
                                </div>
                              )}
                            </div>
                            <div className="p-4">
                              <div className="flex items-start justify-between mb-2">
                                <div className="flex-1">
                                  <h3 className="font-medium text-gray-800 text-lg">{template.name}</h3>
                                  <p className="text-xs sm:text-sm text-gray-500 mt-1">{template.previewDescription}</p>
                                </div>
                                <div className="ml-3 flex-shrink-0 mt-1">
                                  <TemplatePricing
                                      tokenCost={template.tokenCost}
                                      size="small"
                                    />
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                        
                        {/* If we've reached the end of templates but there are more pages */}
                        {currentTemplatePage * templatesPerPage + templatesPerPage >= applicationLetterTemplates.length &&
                         applicationLetterTemplates.length > 0 && (
                          <div className="border-2 border-dashed border-gray-200 rounded-lg overflow-hidden flex items-center justify-center h-80 sm:h-96 md:h-[26rem] p-4">
                            <div className="text-center text-gray-500">
                              <p className="font-medium">Desain profesional lainnya akan segera hadir</p>
                            </div>
                          </div>
                        )}
                      </div>
                      
                      {/* Pagination controls if needed */}
                      {applicationLetterTemplates.length > templatesPerPage && (
                        <div className="flex justify-center mt-4 space-x-2">
                          <button
                            onClick={() => setCurrentTemplatePage(prev => Math.max(0, prev - 1))}
                            disabled={currentTemplatePage === 0}
                            className={`p-2 rounded-full ${currentTemplatePage === 0 ? 'text-gray-400 cursor-not-allowed' : 'text-gray-700 hover:bg-gray-100'}`}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </button>
                          
                          <div className="flex items-center px-3 py-1 bg-gray-100 rounded-md">
                            <span className="text-sm font-medium">
                              {currentTemplatePage + 1} / {Math.ceil(applicationLetterTemplates.length / templatesPerPage)}
                            </span>
                          </div>
                          
                          <button
                            onClick={() => setCurrentTemplatePage(prev =>
                              Math.min(Math.ceil(applicationLetterTemplates.length / templatesPerPage) - 1, prev + 1))}
                            disabled={currentTemplatePage >= Math.ceil(applicationLetterTemplates.length / templatesPerPage) - 1}
                            className={`p-2 rounded-full ${currentTemplatePage >= Math.ceil(applicationLetterTemplates.length / templatesPerPage) - 1 ? 'text-gray-400 cursor-not-allowed' : 'text-gray-700 hover:bg-gray-100'}`}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                            </svg>
                          </button>
                        </div>
                      )}
                    </div>
                    
                    {/* A4 Format Information Banner */}
                    <div className="mt-6 p-3 bg-blue-50 border-l-4 border-blue-400 rounded-r-lg">
                      <p className="text-sm text-blue-800">
                        <span className="font-semibold">📄 Format A4:</span> Semua template menggunakan ukuran kertas A4 standar (210 × 297 mm) untuk hasil cetak yang optimal.
                      </p>
                    </div>

                </div>

                {/* Navigation */}
                <div className="flex justify-between mt-6">
                  {!isGenerating && !isRegenerating && !isStreaming && (
                    <button
                      onClick={goBack}
                      className="inline-flex items-center justify-center gap-2 border border-gray-300 hover:bg-gray-100 text-gray-800 px-6 py-3 rounded-lg font-semibold shadow-sm transition-colors"
                    >
                      <ArrowLeftIcon className="w-5 h-5" />
                      <span className="hidden sm:inline">Kembali</span>
                    </button>
                  )}
                  <button
                    onClick={generateLetter}
                    disabled={isGenerating || isUploading || isLoading || isRegenerating || isStreaming || !selectedTemplateId}
                    className={`inline-flex items-center justify-center gap-2 bg-primary hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold shadow-sm transition-colors ${
                      isGenerating || isStreaming || !selectedTemplateId ? 'opacity-75 cursor-not-allowed' : ''
                    }`}
                  >
                    {isGenerating || isStreaming ? (
                      <span className="flex items-center justify-center">
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        {isStreaming ? streamingProgress || 'Memproses surat lamaran...' : 'Membuat Surat Lamaran...'}
                      </span>
                    ) : (
                      <span className='flex items-center justify-center'>
                        {'Buat Surat - '}
                        <svg className="w-4 h-4 ml-0.5" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M21 6.375C21 9.06739 16.9706 11.25 12 11.25C7.02944 11.25 3 9.06739 3 6.375C3 3.68261 7.02944 1.5 12 1.5C16.9706 1.5 21 3.68261 21 6.375Z" />
                          <path d="M12 12.75C14.6852 12.75 17.1905 12.1637 19.0784 11.1411C19.7684 10.7673 20.4248 10.3043 20.9747 9.75674C20.9915 9.87831 21 10.0011 21 10.125C21 12.8174 16.9706 15 12 15C7.02944 15 3 12.8174 3 10.125C3 10.0011 3.00853 9.8783 3.02529 9.75674C3.57523 10.3043 4.23162 10.7673 4.92161 11.1411C6.80949 12.1637 9.31481 12.75 12 12.75Z" />
                          <path d="M12 16.5C14.6852 16.5 17.1905 15.9137 19.0784 14.8911C19.7684 14.5173 20.4248 14.0543 20.9747 13.5067C20.9915 13.6283 21 13.7511 21 13.875C21 16.5674 16.9706 18.75 12 18.75C7.02944 18.75 3 16.5674 3 13.875C3 13.7511 3.00853 13.6283 3.02529 13.5067C3.57523 14.0543 4.23162 14.5173 4.92161 14.8911C6.80949 15.9137 9.31481 16.5 12 16.5Z" />
                          <path d="M12 20.25C14.6852 20.25 17.1905 19.6637 19.0784 18.6411C19.7684 18.2673 20.4248 17.8043 20.9747 17.2567C20.9915 17.3783 21 17.5011 21 17.625C21 20.3174 16.9706 22.5 12 22.5C7.02944 22.5 3 20.3174 3 17.625C3 17.5011 3.00853 17.3783 3.02529 17.2567C3.57523 17.8043 4.23162 18.2673 4.92161 18.6411C6.80949 19.6637 9.31481 20.25 12 20.25Z" />
                        </svg>
                        {`${applicationLetterTemplates.find(t => t.id === selectedTemplateId)?.tokenCost}`}
                      </span>
                    )}
                  </button>
                </div>

                {/* Error message */}
                {letterGenerationError && (
                  <div className="p-3 mt-6 text-sm bg-red-100 border border-red-200 text-red-800 rounded-md">
                    <div className="flex">
                      <svg className="h-5 w-5 text-red-600 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      <span>{letterGenerationError}</span>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Step 4: Results */}
            {currentStep === 4 && (
              <div>
                <h2 className="text-xl font-semibold mb-4">4. Hasil Surat Lamaran</h2>

                {/* Loading State */}
                {(isGenerating || isStreaming) && !generatedLetter && (
                  <div className="mb-6 text-center py-12">
                    <div className="flex flex-col items-center justify-center space-y-4">
                      <svg className="animate-spin h-12 w-12 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <div className="text-lg font-medium text-gray-900">
                        {isStreaming ? streamingProgress || 'Memproses surat lamaran...' : 'Membuat Surat Lamaran...'}
                      </div>
                      <div className="text-sm text-gray-600">
                        Mohon tunggu, AI sedang membuat surat lamaran Anda...
                      </div>
                    </div>
                  </div>
                )}

                {/* Letter Results */}
                {generatedLetter && (
                  <div className="mt-6">
                    {/* Structured Data Editing Interface */}
                    {isStructuredEditable && generatedLetter.structuredData && editedStructuredData && (
                      <div className="my-6 p-4 border border-blue-200 bg-blue-50 rounded-lg transition-all duration-300 ease-in-out hidden md:block">
                        <h3 className="text-lg font-medium text-gray-800 mb-4">Edit Data Surat</h3>
                        <p className="text-xs text-gray-600 mb-4">
                          Edit bagian-bagian surat di bawah ini. Perubahan akan langsung diterapkan pada template desain.
                        </p>
                        
                        <div className="space-y-8">
                          {/* Header Section */}
                          <div className="bg-gray-50 p-4 rounded-lg">
                            <h4 className="text-md font-medium text-gray-800 mb-4">📅 Header</h4>
                            <div className="space-y-4">
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">Tanggal Surat</label>
                                <input
                                  type="text"
                                  className="w-full border border-gray-300 rounded-md p-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                  value={editedStructuredData.header.date}
                                  onChange={e => setEditedStructuredData(prev => prev ? {
                                    ...prev,
                                    header: { ...prev.header, date: e.target.value }
                                  } : null)}
                                  disabled={isRegenerating}
                                  placeholder="27 Mei 2025"
                                />
                              </div>
                              {editedStructuredData.header.formattedDate !== undefined && (
                                <div>
                                  <label className="block text-sm font-medium text-gray-700 mb-2">Format Tanggal Alternatif (Opsional)</label>
                                  <input
                                    type="text"
                                    className="w-full border border-gray-300 rounded-md p-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    value={editedStructuredData.header.formattedDate || ''}
                                    onChange={e => setEditedStructuredData(prev => prev ? {
                                      ...prev,
                                      header: { ...prev.header, formattedDate: e.target.value }
                                    } : null)}
                                    disabled={isRegenerating}
                                    placeholder="27 May 2025"
                                  />
                                </div>
                              )}
                            </div>
                          </div>

                          {/* Subject Section */}
                          <div className="bg-blue-50 p-4 rounded-lg">
                            <h4 className="text-md font-medium text-gray-800 mb-4">📋 Perihal</h4>
                            <div className="space-y-4">
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">Prefix Perihal</label>
                                <input
                                  type="text"
                                  className="w-full border border-gray-300 rounded-md p-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                  value={editedStructuredData.subject.prefix}
                                  onChange={e => setEditedStructuredData(prev => prev ? {
                                    ...prev,
                                    subject: { ...prev.subject, prefix: e.target.value }
                                  } : null)}
                                  disabled={isRegenerating}
                                  placeholder="Perihal: Lamaran Pekerjaan sebagai"
                                />
                              </div>
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">Posisi yang Dilamar</label>
                                <input
                                  type="text"
                                  className="w-full border border-gray-300 rounded-md p-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                  value={editedStructuredData.subject.position}
                                  onChange={e => setEditedStructuredData(prev => prev ? {
                                    ...prev,
                                    subject: { ...prev.subject, position: e.target.value }
                                  } : null)}
                                  disabled={isRegenerating}
                                  placeholder="Fullstack Developer"
                                />
                              </div>
                            </div>
                          </div>

                          {/* Recipient Section */}
                          <div className="bg-green-50 p-4 rounded-lg">
                            <h4 className="text-md font-medium text-gray-800 mb-4">🏢 Penerima Surat</h4>
                            <div className="space-y-4">
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">Sapaan</label>
                                <input
                                  type="text"
                                  className="w-full border border-gray-300 rounded-md p-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                  value={editedStructuredData.recipient.salutation}
                                  onChange={e => setEditedStructuredData(prev => prev ? {
                                    ...prev,
                                    recipient: { ...prev.recipient, salutation: e.target.value }
                                  } : null)}
                                  disabled={isRegenerating}
                                  placeholder="Yth."
                                />
                              </div>
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">Penerima</label>
                                <input
                                  type="text"
                                  className="w-full border border-gray-300 rounded-md p-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                  value={editedStructuredData.recipient.title}
                                  onChange={e => setEditedStructuredData(prev => prev ? {
                                    ...prev,
                                    recipient: { ...prev.recipient, title: e.target.value }
                                  } : null)}
                                  disabled={isRegenerating}
                                  placeholder="Bapak/Ibu Bagian Sumber Daya Manusia"
                                />
                              </div>
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">Nama Perusahaan</label>
                                <input
                                  type="text"
                                  className="w-full border border-gray-300 rounded-md p-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                  value={editedStructuredData.recipient.company || ''}
                                  onChange={e => setEditedStructuredData(prev => prev ? {
                                    ...prev,
                                    recipient: { ...prev.recipient, company: e.target.value }
                                  } : null)}
                                  disabled={isRegenerating}
                                  placeholder="PT Example Company"
                                />
                              </div>
                              {editedStructuredData.recipient.address && (
                                <div>
                                  <label className="block text-sm font-medium text-gray-700 mb-2">Alamat Perusahaan</label>
                                  {editedStructuredData.recipient.address.map((addressLine, index) => (
                                    <div key={index} className="mb-2">
                                      <div className="flex gap-2">
                                        <input
                                          type="text"
                                          className="flex-1 border border-gray-300 rounded-md p-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                          value={addressLine}
                                          onChange={e => {
                                            if (editedStructuredData.recipient.address) {
                                              const newAddress = [...editedStructuredData.recipient.address];
                                              newAddress[index] = e.target.value;
                                              setEditedStructuredData(prev => prev ? {
                                                ...prev,
                                                recipient: { ...prev.recipient, address: newAddress }
                                              } : null);
                                            }
                                          }}
                                          disabled={isRegenerating}
                                          placeholder={`Alamat baris ${index + 1}`}
                                        />
                                        {editedStructuredData.recipient.address && editedStructuredData.recipient.address.length > 1 && (
                                          <button
                                            type="button"
                                            className="text-red-600 hover:text-red-800 p-2"
                                            onClick={() => {
                                              if (editedStructuredData.recipient.address) {
                                                const newAddress = editedStructuredData.recipient.address.filter((_, i) => i !== index);
                                                setEditedStructuredData(prev => prev ? {
                                                  ...prev,
                                                  recipient: { ...prev.recipient, address: newAddress }
                                                } : null);
                                              }
                                            }}
                                            disabled={isRegenerating}
                                          >
                                            ✕
                                          </button>
                                        )}
                                      </div>
                                    </div>
                                  ))}
                                  <button
                                    type="button"
                                    className="text-blue-600 hover:text-blue-800 text-sm mt-2"
                                    onClick={() => {
                                      if (editedStructuredData.recipient.address) {
                                        const newAddress = [...editedStructuredData.recipient.address, ''];
                                        setEditedStructuredData(prev => prev ? {
                                          ...prev,
                                          recipient: { ...prev.recipient, address: newAddress }
                                        } : null);
                                      } else {
                                        setEditedStructuredData(prev => prev ? {
                                          ...prev,
                                          recipient: { ...prev.recipient, address: [''] }
                                        } : null);
                                      }
                                    }}
                                    disabled={isRegenerating}
                                  >
                                    + Tambah Baris Alamat
                                  </button>
                                </div>
                              )}
                            </div>
                          </div>

                          {/* Body Section */}
                          <div className="bg-purple-50 p-4 rounded-lg">
                            <h4 className="text-md font-medium text-gray-800 mb-4">📄 Isi Surat</h4>
                            <div className="space-y-4">
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">Pembuka Surat</label>
                                <input
                                  type="text"
                                  className="w-full border border-gray-300 rounded-md p-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                  value={editedStructuredData.body.opening}
                                  onChange={e => setEditedStructuredData(prev => prev ? {
                                    ...prev,
                                    body: { ...prev.body, opening: e.target.value }
                                  } : null)}
                                  disabled={isRegenerating}
                                  placeholder="Dengan hormat,"
                                />
                              </div>
                              
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">Isi Surat (Paragraf)</label>
                                {editedStructuredData.body.paragraphs.map((paragraph, index) => (
                                  <div key={index} className="mb-3">
                                    <div className="flex items-center justify-between mb-1">
                                      <label className="block text-xs text-gray-500">Paragraf {index + 1}</label>
                                      {editedStructuredData.body.paragraphs.length > 1 && (
                                        <button
                                          type="button"
                                          className="text-red-600 hover:text-red-800 text-xs"
                                          onClick={() => {
                                            const newParagraphs = editedStructuredData.body.paragraphs.filter((_, i) => i !== index);
                                            setEditedStructuredData(prev => prev ? {
                                              ...prev,
                                              body: { ...prev.body, paragraphs: newParagraphs }
                                            } : null);
                                          }}
                                          disabled={isRegenerating}
                                        >
                                          Hapus Paragraf
                                        </button>
                                      )}
                                    </div>
                                    <textarea
                                      className="w-full border border-gray-300 rounded-md p-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                      rows={3}
                                      value={paragraph}
                                      onChange={e => {
                                        const newParagraphs = [...editedStructuredData.body.paragraphs];
                                        newParagraphs[index] = e.target.value;
                                        setEditedStructuredData(prev => prev ? {
                                          ...prev,
                                          body: { ...prev.body, paragraphs: newParagraphs }
                                        } : null);
                                      }}
                                      disabled={isRegenerating}
                                      placeholder={`Isi paragraf ${index + 1}...`}
                                    />
                                  </div>
                                ))}
                                <button
                                  type="button"
                                  className="text-blue-600 hover:text-blue-800 text-sm mt-2"
                                  onClick={() => {
                                    const newParagraphs = [...editedStructuredData.body.paragraphs, ''];
                                    setEditedStructuredData(prev => prev ? {
                                      ...prev,
                                      body: { ...prev.body, paragraphs: newParagraphs }
                                    } : null);
                                  }}
                                  disabled={isRegenerating}
                                >
                                  + Tambah Paragraf
                                </button>
                              </div>

                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">Penutup Surat</label>
                                <textarea
                                  className="w-full border border-gray-300 rounded-md p-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                  rows={2}
                                  value={editedStructuredData.body.closing}
                                  onChange={e => setEditedStructuredData(prev => prev ? {
                                    ...prev,
                                    body: { ...prev.body, closing: e.target.value }
                                  } : null)}
                                  disabled={isRegenerating}
                                  placeholder="Atas perhatian dan waktu yang Bapak/Ibu berikan, saya ucapkan terima kasih."
                                />
                              </div>
                            </div>
                          </div>

                          {/* Signature Section */}
                          <div className="bg-orange-50 p-4 rounded-lg">
                            <h4 className="text-md font-medium text-gray-800 mb-4">✍️ Tanda Tangan</h4>
                            <div className="space-y-4">
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">Salam Penutup</label>
                                <input
                                  type="text"
                                  className="w-full border border-gray-300 rounded-md p-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                  value={editedStructuredData.signature.farewell}
                                  onChange={e => setEditedStructuredData(prev => prev ? {
                                    ...prev,
                                    signature: { ...prev.signature, farewell: e.target.value }
                                  } : null)}
                                  disabled={isRegenerating}
                                  placeholder="Hormat saya,"
                                />
                              </div>
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">Nama Pengirim</label>
                                <input
                                  type="text"
                                  className="w-full border border-gray-300 rounded-md p-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                  value={editedStructuredData.signature.name}
                                  onChange={e => setEditedStructuredData(prev => prev ? {
                                    ...prev,
                                    signature: { ...prev.signature, name: e.target.value }
                                  } : null)}
                                  disabled={isRegenerating}
                                  placeholder="John Doe"
                                />
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="flex justify-end items-center mt-6">
                          <div className="flex gap-2">
                            <button
                              className="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 transition disabled:opacity-50"
                              onClick={() => setIsStructuredEditable(false)}
                              disabled={isRegenerating}
                            >
                              Batal
                            </button>
                            <button
                              className="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition disabled:opacity-50"
                              onClick={regenerateLetterFromStructuredEdit}
                              disabled={isRegenerating || !editedStructuredData}
                            >
                              {isRegenerating ? (
                                <>
                                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                  </svg>
                                  Memperbarui...
                                </>
                              ) : (
                                <>
                                  <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                                  </svg>
                                  Update Surat
                                </>
                              )}
                            </button>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {!isLetterEditable && !isStructuredEditable && generatedLetter && (
                      <>
                        <div className="mb-2 hidden md:block">
                          <h3 className="text-base font-semibold text-gray-800">Teks Surat</h3>
                        </div>
                        <div className="my-4 p-4 bg-white border border-gray-200 rounded-lg shadow-inner max-h-[800px] overflow-y-auto hidden md:block">
                          <pre className="whitespace-pre-wrap font-sans text-gray-800 text-sm leading-relaxed">{editedLetter || generatedLetter.plainText}</pre>
                        </div>
                        <div className="justify-end gap-2 mt-3 hidden md:flex">
                          <button
                            className="inline-flex items-center px-3 py-1 text-xs font-medium rounded-md shadow-sm text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 transition"
                            onClick={() => {
                              const textToCopy = editedLetter || generatedLetter.plainText || '';
                              if (textToCopy) {
                                navigator.clipboard.writeText(textToCopy);
                                alert('Teks surat disalin ke clipboard!');
                              }
                            }}
                          >
                            <svg className="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"></path>
                            </svg>
                            Salin
                          </button>
                          {generatedLetter.structuredData ? (
                            <button
                              className="px-2 py-1 text-xs font-medium text-primary border border-primary rounded bg-transparent shadow-none hover:bg-primary/10 focus:outline-none focus:ring-2 focus:ring-primary transition-colors disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-transparent"
                              onClick={() => {
                                setIsStructuredEditable(true);
                                setEditedStructuredData(generatedLetter.structuredData!);
                              }}
                              disabled={!generatedLetter.design}
                            >
                              Edit Data Surat
                            </button>
                          ) : (
                            <button
                              className="px-2 py-1 text-xs font-medium text-primary border border-primary rounded bg-transparent shadow-none hover:bg-primary/10 focus:outline-none focus:ring-2 focus:ring-primary transition-colors disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-transparent"
                              onClick={() => {
                                setIsLetterEditable(true);
                                setEditedLetter(generatedLetter.plainText || '');
                              }}
                              disabled={!generatedLetter.design}
                            >
                              Ubah Teks Surat
                            </button>
                          )}
                        </div>
                      </>
                    )}
                    
                    <div className="mt-6">
                      {generatedLetter?.design ? (
                        // Designed letter view with improved display
                        <div>
                          <hr className="border-gray-200 mb-4" />
                          <div className="mb-2">
                            <span className="text-base font-semibold text-gray-800">{generatedLetter.design.name}</span>
                            <span className="block text-sm text-gray-500">{generatedLetter.design.previewDescription}</span>
                          </div>
                          <div className="-mx-6">
                            <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet" />
                            <ZoomableResult content={generatedLetter.design.html} fontFamily={generatedLetter.design.fontFamily} />
                          </div>
                          <div className="py-3 flex justify-end space-x-3">
                            {/* PDF download button with payment flow */}
                            <button
                              onClick={handleDownloadPdf}
                              disabled={isDownloadingPdf}
                              className="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition duration-150 ease-in-out"
                            >
                              {isDownloadingPdf ? (
                                <>
                                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                  </svg>
                                  Memproses...
                                </>
                              ) : (
                                <>
                                  <svg className="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                  </svg>
                                  Unduh PDF
                                </>
                              )}
                            </button>
                          </div>
                        </div>
                       ) : isStreaming ? (
                        // Show loading state while streaming instead of error
                        <div className="p-4 mt-2 text-sm bg-gray-50 border border-gray-200 rounded-md min-h-[400px] flex flex-col items-center justify-center">
                          <svg className="animate-spin h-12 w-12 text-primary mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          <h3 className="text-lg font-medium mb-2 text-gray-700">{streamingProgress || 'Sedang menghasilkan desain surat lamaran...'}</h3>
                          <p className="text-center text-gray-600">Proses ini membutuhkan beberapa detik...</p>
                        </div>
                      ) : (
                        // Only show error if not streaming and no design available
                        <div className="p-4 mt-2 text-sm bg-red-100 border border-red-300 text-red-800 rounded-md min-h-[400px] flex flex-col items-center justify-center">
                          <svg className="h-12 w-12 text-red-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                          </svg>
                          <h3 className="text-lg font-medium mb-2">Terjadi Kesalahan</h3>
                          <p className="text-center">Terjadi kesalahan saat memproses surat lamaran. Silakan coba lagi</p>
                        </div>
                      )}
                    </div>

                    {generatedLetter && (
                      <>
                        <div className="p-3 bg-blue-50 text-blue-800 rounded-lg border border-blue-200 text-xs mt-2">
                            <div className='flex gap-2'>
                              <svg className="w-5 h-5 text-blue-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M12 20a8 8 0 100-16 8 8 0 000 16z"></path>
                              </svg>
                              <span className='text-sm'>Jangan lupa untuk mengisi bagian yang diperlukan dan melampirkan dokumen tambahan yang disebutkan saat melamar.</span>
                            </div>
                        </div>
                        <p className="text-xs text-gray-500 italic mt-4">
                          *Surat lamaran ini dibuat dengan AI dan mungkin tidak sepenuhnya akurat. Silakan periksa ulang dan edit jika diperlukan. Anda dapat membuat ulang berkali-kali untuk hasil yang lebih sesuai.
                        </p>
                      </>
                    )}
                  </div>
                )}

                {/* Navigation */}
                <div className="flex justify-between mt-6">
                  {!isGenerating && !isRegenerating && !isStreaming && (
                    <button
                      onClick={goBack}
                      className="inline-flex items-center justify-center gap-2 border border-gray-300 hover:bg-gray-100 text-gray-800 px-6 py-3 rounded-lg font-semibold shadow-sm transition-colors"
                    >
                      <ArrowLeftIcon className="w-5 h-5" />
                      <span className="hidden sm:inline">Kembali</span>
                    </button>
                  )}

                  {generatedLetter && (
                    <button
                      onClick={() => {
                        // Reset current letter and regenerate
                        setGeneratedLetter(null);
                        setIsLetterEditable(false);
                        generateLetter();
                      }}
                      className={`inline-flex items-center justify-center gap-2 bg-gray-100 hover:bg-gray-200 text-gray-700 px-6 py-3 rounded-lg font-semibold shadow-sm transition-colors ${isGenerating ? 'opacity-75 cursor-not-allowed' : ''}`}
                      disabled={isGenerating}
                    >
                      {isGenerating ? (
                        <>
                          <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Membuat Ulang...
                        </>
                      ) : (
                        <>
                          <span className='flex items-center justify-center'>
                            {'Buat Ulang - '}
                            <svg className="w-4 h-4 ml-0.5" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M21 6.375C21 9.06739 16.9706 11.25 12 11.25C7.02944 11.25 3 9.06739 3 6.375C3 3.68261 7.02944 1.5 12 1.5C16.9706 1.5 21 3.68261 21 6.375Z" />
                              <path d="M12 12.75C14.6852 12.75 17.1905 12.1637 19.0784 11.1411C19.7684 10.7673 20.4248 10.3043 20.9747 9.75674C20.9915 9.87831 21 10.0011 21 10.125C21 12.8174 16.9706 15 12 15C7.02944 15 3 12.8174 3 10.125C3 10.0011 3.00853 9.8783 3.02529 9.75674C3.57523 10.3043 4.23162 10.7673 4.92161 11.1411C6.80949 12.1637 9.31481 12.75 12 12.75Z" />
                              <path d="M12 16.5C14.6852 16.5 17.1905 15.9137 19.0784 14.8911C19.7684 14.5173 20.4248 14.0543 20.9747 13.5067C20.9915 13.6283 21 13.7511 21 13.875C21 16.5674 16.9706 18.75 12 18.75C7.02944 18.75 3 16.5674 3 13.875C3 13.7511 3.00853 13.6283 3.02529 13.5067C3.57523 14.0543 4.23162 14.5173 4.92161 14.8911C6.80949 15.9137 9.31481 16.5 12 16.5Z" />
                              <path d="M12 20.25C14.6852 20.25 17.1905 19.6637 19.0784 18.6411C19.7684 18.2673 20.4248 17.8043 20.9747 17.2567C20.9915 17.3783 21 17.5011 21 17.625C21 20.3174 16.9706 22.5 12 22.5C7.02944 22.5 3 20.3174 3 17.625C3 17.5011 3.00853 17.3783 3.02529 17.2567C3.57523 17.8043 4.23162 18.2673 4.92161 18.6411C6.80949 19.6637 9.31481 20.25 12 20.25Z" />
                            </svg>
                            {`${applicationLetterTemplates.find(t => t.id === selectedTemplateId)?.tokenCost}`}
                          </span>
                        </>
                      )}
                    </button>
                  )}
                </div>

                {/* Error message */}
                {letterGenerationError && (
                  <div className="p-3 mt-6 text-sm bg-red-100 border border-red-200 text-red-800 rounded-md">
                    <div className="flex">
                      <svg className="h-5 w-5 text-red-600 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      <div className="flex-1">
                        <span>{letterGenerationError}</span>
                        {letterGenerationError.includes('Token Anda tidak cukup') && (
                          <div className="mt-3">
                            <button
                              onClick={() => window.location.href = '/buy-tokens'}
                              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                            >
                              Beli Token
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {/* Error message for letter result */}
                {letterResultError && (
                  <div className="p-3 mt-6 text-sm bg-red-100 border border-red-200 text-red-800 rounded-md">
                    <div className="flex">
                      <svg className="h-5 w-5 text-red-600 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      <div className="flex-1">
                        <span>{letterResultError}</span>
                        {letterResultError.includes('Token Anda tidak cukup') && (
                          <div className="mt-3">
                            <button
                              onClick={() => window.location.href = '/buy-tokens'}
                              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                            >
                              Beli Token
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Mobile Bottom Navigation - only show on Step 4 when letter is generated */}
          {currentStep === 4 && generatedLetter && (
            <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-3 py-4 pb-safe sm:px-4 md:hidden">
              <div className="flex justify-between items-center gap-2 sm:gap-3">
                {/* Plain Text View Button */}
                <button
                  onClick={() => setIsPlainTextSheetOpen(true)}
                  className="flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-lg font-medium transition-colors bg-gray-100 hover:bg-gray-200 text-gray-700"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                  <span>Teks</span>
                </button>

                {/* Edit Button - only show if structured data is available */}
                {generatedLetter.structuredData && (
                  <button
                    onClick={() => {
                      setIsEditSheetOpen(true);
                      setIsStructuredEditable(true);
                      setEditedStructuredData(generatedLetter.structuredData!);
                    }}
                    className="flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-lg font-medium transition-colors bg-gray-100 hover:bg-gray-200 text-gray-700"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    <span>Edit</span>
                  </button>
                )}
              </div>
            </div>
          )}

          {/* Show History Button - only show for logged in users */}
          {user && (
            <div className="text-center mt-8">
              <button
                onClick={() => setShowHistoryModal(true)}
                className="inline-flex items-center gap-2 px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg font-medium transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Lihat Riwayat Surat
              </button>
            </div>
          )}
        </div>
      </section>

      <Footer />

      {/* Plain Text Bottom Sheet */}
      <BottomSheet
        isOpen={isPlainTextSheetOpen}
        onClose={() => setIsPlainTextSheetOpen(false)}
        title="Teks Surat"
      >
        {generatedLetter && (
          <div>
            <div className="mb-4 p-4 bg-white border border-gray-200 rounded-lg shadow-inner max-h-[60vh] overflow-y-auto">
              <pre className="whitespace-pre-wrap font-sans text-gray-800 text-sm leading-relaxed">
                {editedLetter || generatedLetter.plainText}
              </pre>
            </div>
            <div className="flex justify-end gap-2">
              <button
                className="inline-flex items-center px-3 py-2 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 transition"
                onClick={() => {
                  setIsPlainTextSheetOpen(false);
                  setIsEditSheetOpen(true);
                  setIsStructuredEditable(true);
                  setEditedStructuredData(generatedLetter.structuredData!);
                }}
              >
                <svg className="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Edit
              </button>
              <button
                className="inline-flex items-center px-3 py-2 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 transition"
                onClick={() => {
                  const textToCopy = editedLetter || generatedLetter.plainText || '';
                  if (textToCopy) {
                    navigator.clipboard.writeText(textToCopy);
                    alert('Teks surat disalin ke clipboard!');
                  }
                }}
              >
                <svg className="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"></path>
                </svg>
                Salin
              </button>
            </div>
          </div>
        )}
      </BottomSheet>

      {/* Edit Bottom Sheet */}
      <BottomSheet
        isOpen={isEditSheetOpen}
        onClose={() => setIsEditSheetOpen(false)}
        title="Edit Data Surat"
      >
        {isStructuredEditable && generatedLetter?.structuredData && editedStructuredData && (
          <div>
            <p className="text-xs text-gray-600 mb-4">
              Edit bagian-bagian surat di bawah ini. Perubahan akan langsung diterapkan pada template desain.
            </p>

            <div className="space-y-8">
              {/* Header Section */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="text-md font-medium text-gray-800 mb-4">📅 Header</h4>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Tanggal Surat</label>
                    <input
                      type="text"
                      className="w-full border border-gray-300 rounded-md p-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      value={editedStructuredData.header.date}
                      onChange={e => setEditedStructuredData(prev => prev ? {
                        ...prev,
                        header: { ...prev.header, date: e.target.value }
                      } : null)}
                      disabled={isRegenerating}
                      placeholder="27 Mei 2025"
                    />
                  </div>
                  {editedStructuredData.header.formattedDate !== undefined && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Format Tanggal Alternatif (Opsional)</label>
                      <input
                        type="text"
                        className="w-full border border-gray-300 rounded-md p-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        value={editedStructuredData.header.formattedDate || ''}
                        onChange={e => setEditedStructuredData(prev => prev ? {
                          ...prev,
                          header: { ...prev.header, formattedDate: e.target.value }
                        } : null)}
                        disabled={isRegenerating}
                        placeholder="27 May 2025"
                      />
                    </div>
                  )}
                </div>
              </div>

              {/* Subject Section */}
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="text-md font-medium text-gray-800 mb-4">📋 Perihal</h4>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Prefix Perihal</label>
                    <input
                      type="text"
                      className="w-full border border-gray-300 rounded-md p-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      value={editedStructuredData.subject.prefix}
                      onChange={e => setEditedStructuredData(prev => prev ? {
                        ...prev,
                        subject: { ...prev.subject, prefix: e.target.value }
                      } : null)}
                      disabled={isRegenerating}
                      placeholder="Perihal: Lamaran Pekerjaan sebagai"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Posisi yang Dilamar</label>
                    <input
                      type="text"
                      className="w-full border border-gray-300 rounded-md p-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      value={editedStructuredData.subject.position}
                      onChange={e => setEditedStructuredData(prev => prev ? {
                        ...prev,
                        subject: { ...prev.subject, position: e.target.value }
                      } : null)}
                      disabled={isRegenerating}
                      placeholder="Fullstack Developer"
                    />
                  </div>
                </div>
              </div>

              {/* Recipient Section */}
              <div className="bg-green-50 p-4 rounded-lg">
                <h4 className="text-md font-medium text-gray-800 mb-4">🏢 Penerima Surat</h4>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Sapaan</label>
                    <input
                      type="text"
                      className="w-full border border-gray-300 rounded-md p-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      value={editedStructuredData.recipient.salutation}
                      onChange={e => setEditedStructuredData(prev => prev ? {
                        ...prev,
                        recipient: { ...prev.recipient, salutation: e.target.value }
                      } : null)}
                      disabled={isRegenerating}
                      placeholder="Yth."
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Penerima</label>
                    <input
                      type="text"
                      className="w-full border border-gray-300 rounded-md p-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      value={editedStructuredData.recipient.title}
                      onChange={e => setEditedStructuredData(prev => prev ? {
                        ...prev,
                        recipient: { ...prev.recipient, title: e.target.value }
                      } : null)}
                      disabled={isRegenerating}
                      placeholder="Bapak/Ibu Bagian Sumber Daya Manusia"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Nama Perusahaan</label>
                    <input
                      type="text"
                      className="w-full border border-gray-300 rounded-md p-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      value={editedStructuredData.recipient.company || ''}
                      onChange={e => setEditedStructuredData(prev => prev ? {
                        ...prev,
                        recipient: { ...prev.recipient, company: e.target.value }
                      } : null)}
                      disabled={isRegenerating}
                      placeholder="PT Example Company"
                    />
                  </div>
                  {editedStructuredData.recipient.address && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Alamat Perusahaan</label>
                      {editedStructuredData.recipient.address.map((addressLine, index) => (
                        <div key={index} className="mb-2">
                          <div className="flex gap-2">
                            <input
                              type="text"
                              className="flex-1 border border-gray-300 rounded-md p-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              value={addressLine}
                              onChange={e => {
                                if (editedStructuredData.recipient.address) {
                                  const newAddress = [...editedStructuredData.recipient.address];
                                  newAddress[index] = e.target.value;
                                  setEditedStructuredData(prev => prev ? {
                                    ...prev,
                                    recipient: { ...prev.recipient, address: newAddress }
                                  } : null);
                                }
                              }}
                              disabled={isRegenerating}
                              placeholder={`Alamat baris ${index + 1}`}
                            />
                            {editedStructuredData.recipient.address && editedStructuredData.recipient.address.length > 1 && (
                              <button
                                type="button"
                                className="text-red-600 hover:text-red-800 p-2"
                                onClick={() => {
                                  if (editedStructuredData.recipient.address) {
                                    const newAddress = editedStructuredData.recipient.address.filter((_, i) => i !== index);
                                    setEditedStructuredData(prev => prev ? {
                                      ...prev,
                                      recipient: { ...prev.recipient, address: newAddress }
                                    } : null);
                                  }
                                }}
                                disabled={isRegenerating}
                              >
                                ✕
                              </button>
                            )}
                          </div>
                        </div>
                      ))}
                      <button
                        type="button"
                        className="text-blue-600 hover:text-blue-800 text-sm mt-2"
                        onClick={() => {
                          if (editedStructuredData.recipient.address) {
                            const newAddress = [...editedStructuredData.recipient.address, ''];
                            setEditedStructuredData(prev => prev ? {
                              ...prev,
                              recipient: { ...prev.recipient, address: newAddress }
                            } : null);
                          } else {
                            setEditedStructuredData(prev => prev ? {
                              ...prev,
                              recipient: { ...prev.recipient, address: [''] }
                            } : null);
                          }
                        }}
                        disabled={isRegenerating}
                      >
                        + Tambah Baris Alamat
                      </button>
                    </div>
                  )}
                </div>
              </div>

              {/* Body Section */}
              <div className="bg-purple-50 p-4 rounded-lg">
                <h4 className="text-md font-medium text-gray-800 mb-4">📄 Isi Surat</h4>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Pembuka Surat</label>
                    <input
                      type="text"
                      className="w-full border border-gray-300 rounded-md p-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      value={editedStructuredData.body.opening}
                      onChange={e => setEditedStructuredData(prev => prev ? {
                        ...prev,
                        body: { ...prev.body, opening: e.target.value }
                      } : null)}
                      disabled={isRegenerating}
                      placeholder="Dengan hormat,"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Isi Surat (Paragraf)</label>
                    {editedStructuredData.body.paragraphs.map((paragraph, index) => (
                      <div key={index} className="mb-3">
                        <div className="flex items-center justify-between mb-1">
                          <label className="block text-xs text-gray-500">Paragraf {index + 1}</label>
                          {editedStructuredData.body.paragraphs.length > 1 && (
                            <button
                              type="button"
                              className="text-red-600 hover:text-red-800 text-xs"
                              onClick={() => {
                                const newParagraphs = editedStructuredData.body.paragraphs.filter((_, i) => i !== index);
                                setEditedStructuredData(prev => prev ? {
                                  ...prev,
                                  body: { ...prev.body, paragraphs: newParagraphs }
                                } : null);
                              }}
                              disabled={isRegenerating}
                            >
                              Hapus Paragraf
                            </button>
                          )}
                        </div>
                        <textarea
                          className="w-full border border-gray-300 rounded-md p-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          rows={3}
                          value={paragraph}
                          onChange={e => {
                            const newParagraphs = [...editedStructuredData.body.paragraphs];
                            newParagraphs[index] = e.target.value;
                            setEditedStructuredData(prev => prev ? {
                              ...prev,
                              body: { ...prev.body, paragraphs: newParagraphs }
                            } : null);
                          }}
                          disabled={isRegenerating}
                          placeholder={`Isi paragraf ${index + 1}...`}
                        />
                      </div>
                    ))}
                    <button
                      type="button"
                      className="text-blue-600 hover:text-blue-800 text-sm mt-2"
                      onClick={() => {
                        const newParagraphs = [...editedStructuredData.body.paragraphs, ''];
                        setEditedStructuredData(prev => prev ? {
                          ...prev,
                          body: { ...prev.body, paragraphs: newParagraphs }
                        } : null);
                      }}
                      disabled={isRegenerating}
                    >
                      + Tambah Paragraf
                    </button>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Penutup Surat</label>
                    <textarea
                      className="w-full border border-gray-300 rounded-md p-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      rows={2}
                      value={editedStructuredData.body.closing}
                      onChange={e => setEditedStructuredData(prev => prev ? {
                        ...prev,
                        body: { ...prev.body, closing: e.target.value }
                      } : null)}
                      disabled={isRegenerating}
                      placeholder="Atas perhatian dan waktu yang Bapak/Ibu berikan, saya ucapkan terima kasih."
                    />
                  </div>
                </div>
              </div>

              {/* Signature Section */}
              <div className="bg-orange-50 p-4 rounded-lg">
                <h4 className="text-md font-medium text-gray-800 mb-4">✍️ Tanda Tangan</h4>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Salam Penutup</label>
                    <input
                      type="text"
                      className="w-full border border-gray-300 rounded-md p-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      value={editedStructuredData.signature.farewell}
                      onChange={e => setEditedStructuredData(prev => prev ? {
                        ...prev,
                        signature: { ...prev.signature, farewell: e.target.value }
                      } : null)}
                      disabled={isRegenerating}
                      placeholder="Hormat saya,"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Nama Pengirim</label>
                    <input
                      type="text"
                      className="w-full border border-gray-300 rounded-md p-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      value={editedStructuredData.signature.name}
                      onChange={e => setEditedStructuredData(prev => prev ? {
                        ...prev,
                        signature: { ...prev.signature, name: e.target.value }
                      } : null)}
                      disabled={isRegenerating}
                      placeholder="John Doe"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end items-center mt-6">
              <div className="flex gap-2">
                <button
                  className="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 transition disabled:opacity-50"
                  onClick={() => {
                    setIsStructuredEditable(false);
                    setIsEditSheetOpen(false);
                  }}
                  disabled={isRegenerating}
                >
                  Batal
                </button>
                <button
                  className="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition disabled:opacity-50"
                  onClick={async () => {
                    await regenerateLetterFromStructuredEdit();
                    setIsEditSheetOpen(false);
                  }}
                  disabled={isRegenerating || !editedStructuredData}
                >
                  {isRegenerating ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Memperbarui...
                    </>
                  ) : (
                    <>
                      <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      Update Surat
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}
      </BottomSheet>
    </main>
  );
}
