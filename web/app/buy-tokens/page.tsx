'use client';

import { useState } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { authPost } from '@/lib/authFetch';
import { useAnalytics } from '@/hooks/useAnalytics';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import Seo from '@/components/Seo';
import Toast from '@/components/Toast';
import { useRouter } from 'next/navigation';

interface TokenPackage {
  id: 'coba' | 'hemat' | 'pro';
  name: string;
  tokens: number;
  bonusTokens: number;
  price: number;
  priceFormatted: string;
  popular?: boolean;
  description: string;
  independencePromo?: boolean;
}

const tokenPackages: TokenPackage[] = [
  {
    id: 'coba',
    name: 'Eksplora<PERSON>',
    tokens: 20,
    bonusTokens: 0,
    price: 10000,
    priceFormatted: 'Rp10.000',
    description: 'Paket starter untuk mencoba fitur premium'
  },
  {
    id: 'hemat',
    name: '<PERSON>ap Melamar',
    tokens: 40,
    bonusTokens: 20,
    price: 20000,
    priceFormatted: 'Rp20.000',
    popular: true,
    independencePromo: true,
    description: 'Paket terpopuler dengan bonus token spesial Kemerdekaan RI!'
  },
  {
    id: 'pro',
    name: 'Pejuang Karir',
    tokens: 100,
    bonusTokens: 20,
    price: 50000,
    priceFormatted: 'Rp50.000',
    description: 'Paket terbaik untuk pencari kerja aktif'
  }
];

export default function BuyTokensPage() {
  const auth = useAuth();
  const { user, profile, profileLoading } = auth;
  const { trackEvent } = useAnalytics();
  const router = useRouter();
  const [selectedPackage, setSelectedPackage] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [toast, setToast] = useState<{ message: string; type: 'success' | 'error'; show: boolean }>({ message: '', type: 'success', show: false });
  const [showMaintenanceDialog, setShowMaintenanceDialog] = useState(false);

  const handlePurchase = async (packageId: string) => {
    if (!user) {
      setToast({ message: 'Anda harus login untuk membeli token', type: 'error', show: true });
      return;
    }

    // TEMPORARY: Show maintenance dialog instead of processing payment
    // setShowMaintenanceDialog(true);
    // trackEvent('token_purchase_maintenance_shown', { package: packageId });
    // return;

    // Original payment processing code (temporarily disabled)
    setSelectedPackage(packageId);
    setIsProcessing(true);

    try {
      trackEvent('token_purchase_initiated', { package: packageId });

      const response = await authPost('/api/payment/create', {
        tokenPackage: packageId
      });

      const data = await response.json();

      if (data.ok && data.invoice_url) {
        trackEvent('token_payment_redirect', { package: packageId });
        window.location.href = data.invoice_url;
      } else {
        throw new Error(data.error || 'Gagal membuat pembayaran');
      }
    } catch (error: any) {
      console.error('Error creating token purchase:', error);
      setToast({
        message: error.message || 'Terjadi kesalahan saat memproses pembayaran',
        type: 'error',
        show: true
      });
      trackEvent('token_purchase_error', { package: packageId, error: error.message });
    } finally {
      setIsProcessing(false);
      setSelectedPackage(null);
    }
  };

  return (
    <main className="min-h-screen flex flex-col pt-16">
      <Toast
        show={toast.show}
        message={toast.message}
        type={toast.type}
        onClose={() => setToast({ ...toast, show: false })}
      />

      {/* Maintenance Dialog */}
      {showMaintenanceDialog && (
        <div className="fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex flex-row items-center justify-between gap-4">
                <h3 className="text-lg font-medium text-gray-900">
                  Dalam Pemeliharaan
                </h3>
                <button
                  onClick={() => setShowMaintenanceDialog(false)}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
            <div className="px-6 py-4">
              <div className="text-center">
                <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 mb-4">
                  <svg className="h-6 w-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">
                  Fitur Pembelian Token Sedang Diperbaiki
                </h4>
                <p className="text-gray-600 mb-6">
                  Maaf atas ketidaknyamanannya. Sistem pembelian token sedang dalam pemeliharaan untuk meningkatkan pengalaman Anda. Silakan coba lagi nanti.
                </p>
                <button
                  onClick={() => setShowMaintenanceDialog(false)}
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors"
                >
                  Mengerti
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      <Seo
        title="Beli Token - Gigsta"
        description="Beli token untuk menggunakan fitur premium Gigsta"
      />
      <Navbar auth={auth} />

      {/* Independence Day Promo Banner */}
      <div className="bg-gradient-to-r from-red-600 via-white to-red-600 py-4 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white bg-opacity-95 rounded-xl p-6 shadow-lg border-2 border-red-200">
            <div className="text-center">
              <div className="flex items-center justify-center mb-3">
                <span className="text-3xl mr-2">🇮🇩</span>
                <h2 className="text-2xl font-bold text-red-600">PROMO KEMERDEKAAN RI KE-80</h2>
                <span className="text-3xl ml-2">🇮🇩</span>
              </div>
              <p className="text-lg text-gray-800 font-semibold mb-2">
                Rayakan Kemerdekaan Indonesia dengan Bonus Token Ekstra!
              </p>
              <div className="inline-flex items-center bg-red-100 text-red-800 px-4 py-2 rounded-full text-sm font-medium">
                <span className="mr-2">⏰</span>
                Promo Terbatas - Berlaku sampai 31 Agustus 2024
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Beli Token</h1>
            <p className="text-lg text-gray-600 mb-6">
              Pilih paket token yang sesuai dengan kebutuhan Anda
            </p>
          </div>

          {/* Token Packages */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
            {tokenPackages.map((pkg) => (
              <div
                key={pkg.id}
                className={`relative bg-white rounded-xl shadow-lg border-2 transition-all duration-200 hover:shadow-xl ${
                  pkg.independencePromo
                    ? 'border-red-500 ring-2 ring-red-200 bg-gradient-to-br from-red-50 to-white'
                    : pkg.popular
                    ? 'border-blue-500 ring-2 ring-blue-200'
                    : 'border-gray-200 hover:border-blue-300'
                }`}
              >
                {/* Independence Day Promo Badge */}
                {pkg.independencePromo && (
                  <div className="absolute -top-5 left-3 right-3 z-10">
                    <div className="relative">
                      <div className="bg-gradient-to-r from-red-600 via-red-500 to-red-600 text-white px-4 py-2 rounded-lg text-xs font-bold shadow-xl border-2 border-red-400 relative overflow-hidden">
                        {/* Sparkle effect background */}
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
                        <div className="relative flex items-center justify-center gap-1">
                          <span className="tracking-wide">🇮🇩 KEMERDEKAAN</span>
                        </div>
                        {/* Decorative corners */}
                        <div className="absolute top-0 left-0 w-0 h-0 border-l-[6px] border-t-[6px] border-l-yellow-400 border-t-transparent"></div>
                        <div className="absolute top-0 right-0 w-0 h-0 border-r-[6px] border-t-[6px] border-r-yellow-400 border-t-transparent"></div>
                        <div className="absolute bottom-0 left-0 w-0 h-0 border-l-[6px] border-b-[6px] border-l-yellow-400 border-b-transparent"></div>
                        <div className="absolute bottom-0 right-0 w-0 h-0 border-r-[6px] border-b-[6px] border-r-yellow-400 border-b-transparent"></div>
                      </div>
                    </div>
                  </div>
                )}
                
                {/* Popular Badge (for non-promo packages) */}
                {pkg.popular && !pkg.independencePromo && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                      Terpopuler
                    </span>
                  </div>
                )}
                
                <div className="p-8">
                  <div className="text-center mb-6">
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">{pkg.name}</h3>
                    <p className="text-gray-600 text-sm">{pkg.description}</p>
                  </div>
                  
                  <div className="text-center mb-6">
                    <div className="text-4xl font-bold text-blue-600 mb-2">{pkg.priceFormatted}</div>
                    <div className="text-lg text-gray-700">
                      {pkg.tokens} Token
                      {pkg.bonusTokens > 0 && (
                        <span className={`font-semibold ${pkg.independencePromo ? 'text-red-600' : 'text-green-600'}`}>
                          {pkg.independencePromo && (
                            <span className="line-through text-gray-400 mr-2">+ 5 Bonus</span>
                          )}
                          + {pkg.bonusTokens} Bonus
                          {pkg.independencePromo && <span className="text-red-600 text-sm ml-1">🎉</span>}
                        </span>
                      )}
                    </div>
                    <div className="text-sm text-gray-500 mt-1">
                      Total: {pkg.tokens + pkg.bonusTokens} Token
                      {pkg.independencePromo && (
                        <span className="block text-red-600 font-semibold text-xs mt-1">
                          Bonus Extra +15 Token!
                        </span>
                      )}
                    </div>
                  </div>
                  
                  <button
                    onClick={() => handlePurchase(pkg.id)}
                    disabled={isProcessing}
                    className={`w-full py-3 px-6 rounded-lg font-semibold transition-colors ${
                      pkg.independencePromo
                        ? 'bg-gradient-to-r from-red-600 to-red-700 text-white hover:from-red-700 hover:to-red-800 disabled:from-red-400 disabled:to-red-500'
                        : pkg.popular
                        ? 'bg-blue-600 text-white hover:bg-blue-700 disabled:bg-blue-400'
                        : 'bg-gray-800 text-white hover:bg-gray-900 disabled:bg-gray-400'
                    } ${isProcessing ? 'cursor-not-allowed' : 'cursor-pointer'}`}
                  >
                    {isProcessing && selectedPackage === pkg.id ? (
                      <div className="flex items-center justify-center">
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Memproses...
                      </div>
                    ) : (
                      'Beli Sekarang'
                    )}
                  </button>
                </div>
              </div>
            ))}
          </div>

          {/* Token Usage Guide */}
          <div className="bg-white rounded-xl shadow-lg p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">Panduan Penggunaan Token</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="text-center bg-gray-50 rounded-lg p-6">
                <div className="text-3xl font-bold text-blue-600 mb-2">5 Token</div>
                <div className="text-lg font-semibold text-gray-800 mb-2">Email Lamaran</div>
                <div className="text-sm text-gray-600">Untuk membuat email lamaran kerja profesional</div>
              </div>
              <div className="text-center bg-gray-50 rounded-lg p-6">
                <div className="text-3xl font-bold text-blue-600 mb-2">10 Token</div>
                <div className="text-lg font-semibold text-gray-800 mb-2">Surat Lamaran Standar</div>
                <div className="text-sm text-gray-600">Untuk membuat surat lamaran dengan desain standar</div>
              </div>
              <div className="text-center bg-gray-50 rounded-lg p-6">
                <div className="text-3xl font-bold text-blue-600 mb-2">15 Token</div>
                <div className="text-lg font-semibold text-gray-800 mb-2">Surat Lamaran Premium</div>
                <div className="text-sm text-gray-600">Untuk membuat surat lamaran dengan desain premium</div>
              </div>
              <div className="text-center bg-gray-50 rounded-lg p-6">
                <div className="text-3xl font-bold text-green-600 mb-2">15 Token</div>
                <div className="text-lg font-semibold text-gray-800 mb-2">CV Manual</div>
                <div className="text-sm text-gray-600">Untuk mengunduh CV yang dibuat secara manual</div>
              </div>
              <div className="text-center bg-gray-50 rounded-lg p-6">
                <div className="text-3xl font-bold text-green-600 mb-2">25 Token</div>
                <div className="text-lg font-semibold text-gray-800 mb-2">CV Spesifik Pekerjaan</div>
                <div className="text-sm text-gray-600">Untuk membuat CV yang disesuaikan dengan lowongan kerja menggunakan AI</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </main>
  );
}
